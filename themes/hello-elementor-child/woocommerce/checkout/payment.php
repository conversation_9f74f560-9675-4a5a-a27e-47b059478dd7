<?php

defined( 'ABSPATH' ) || exit;

if ( ! wp_doing_ajax() ) {
	do_action( 'woocommerce_review_order_before_payment' );
}
?>
<div id="payment" class="woocommerce-checkout-payment">

    <h3><?php echo esc_html__('Payment Method','woocommerce'); ?></h3>

	<?php if ( WC()->cart->needs_payment() ) : ?>
		<ul class="wc_payment_methods payment_methods methods">
			<?php
			if ( ! empty( $available_gateways ) ) {
				foreach ( $available_gateways as $gateway ) {
					wc_get_template( 'checkout/payment-method.php', array( 'gateway' => $gateway) );
                }
			} else {
				echo '<li>';
				wc_print_notice( apply_filters( 'woocommerce_no_available_payment_methods_message', WC()->customer->get_billing_country() ? esc_html__( 'Sorry, it seems that there are no available payment methods. Please contact us if you require assistance or wish to make alternate arrangements.', 'woocommerce' ) : esc_html__( 'Please fill in your details above to see available payment methods.', 'woocommerce' ) ), 'notice' ); // phpcs:ignore WooCommerce.Commenting.CommentHooks.MissingHookComment
				echo '</li>';
			}
			?>
		</ul>
	<?php endif; ?>
    <div class="see-more-block">
        <a href="#" class="see-more-payments">
            <span><?php echo esc_html__('See More', 'woocommerce'); ?></span>
            <svg width="25" height="18" viewBox="0 0 25 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_ddd_1_2324)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M5.34525 10.7208C5.79312 11.1036 6.5053 11.0912 6.93594 10.6931L12.5 5.44277L18.0641 10.6931C18.4947 11.0912 19.2069 11.1036 19.6547 10.7208C20.1026 10.338 20.1166 9.705 19.6859 9.30689L13.3109 3.30689C13.0988 3.11081 12.806 3 12.5 3C12.194 3 11.9012 3.11081 11.6891 3.30689L5.31406 9.30689C4.88342 9.705 4.89739 10.338 5.34525 10.7208Z" fill="url(#paint0_linear_1_2324)"/>
                </g>
                <defs>
                    <filter id="filter0_ddd_1_2324" x="0" y="0" width="25" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="1"/>
                        <feGaussianBlur stdDeviation="0.5"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_2324"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect2_dropShadow_1_2324"/>
                        <feOffset/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0.403922 0 0 0 0 0.431373 0 0 0 0 0.462745 0 0 0 0.16 0"/>
                        <feBlend mode="normal" in2="effect1_dropShadow_1_2324" result="effect2_dropShadow_1_2324"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="2"/>
                        <feGaussianBlur stdDeviation="2.5"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0.403922 0 0 0 0 0.431373 0 0 0 0 0.462745 0 0 0 0.08 0"/>
                        <feBlend mode="normal" in2="effect2_dropShadow_1_2324" result="effect3_dropShadow_1_2324"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_1_2324" result="shape"/>
                    </filter>
                    <linearGradient id="paint0_linear_1_2324" x1="5" y1="7" x2="20" y2="7" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#C52233"/>
                        <stop offset="1" stop-color="#FF4747"/>
                    </linearGradient>
                </defs>
            </svg>
        </a>
    </div>
	<!-- Payment button moved to order summary -->

<!--    <div class="copyright-text copyright-text-bottom">-->
<!--        --><?php //echo esc_html__('Copyright © 2024 NergetixPay', 'woocommerce'); ?>
<!--    </div>-->

</div>
<script>
    jQuery(document).ready(function($) {

        payment_method_box_height();

        function payment_method_box_height(count = 4){
            if($('body form.woocommerce-checkout').hasClass('see-all-active')){
                return;
            }

            var payments_methods_box_height = 0;
            $('body ul.wc_payment_methods li').each(function (i,e){
                if(count !== 0 && i >= count){
                    return;
                }
                payments_methods_box_height += $(this).outerHeight() + 8;
            });

            if(payments_methods_box_height > 0){
                $('body ul.wc_payment_methods').css('height', payments_methods_box_height);
            }
        }

        // Hide see-more button if 4 or fewer payment methods
        if($('body ul.wc_payment_methods li').length <= 4) {
            $('.see-more-block').hide();
        }

        $('body').on('click', '.see-all-active .see-more-payments', function(e) {
            e.preventDefault();
            $('body form.woocommerce-checkout').removeClass('see-all-active');
            payment_method_box_height();
            $('ul.wc_payment_methods').css('transition', '0.3s ease');
            $('body .wc_payment_methods li').css('display', 'none');
            $('body .wc_payment_methods li:nth-child(-n+4)').css('display', 'flex');
            $('body .wc_payment_methods li:nth-child(-n+4)').css('display', '-webkit-box');
            $('body .wc_payment_methods li:nth-child(-n+4)').css('display', '-ms-flexbox');
            $(this).find('span').text('See More');
        });

        $('body').on('click', 'form.woocommerce-checkout:not(.see-all-active) .see-more-payments', function(e) {
            e.preventDefault();
            payment_method_box_height(0);
            $('ul.wc_payment_methods').css('transition', '0.3s ease');
            $('body form.woocommerce-checkout').addClass('see-all-active');
            $('body .wc_payment_methods li').css('display', 'flex');
            $('body .wc_payment_methods li').css('display', '-webkit-box');
            $('body .wc_payment_methods li').css('display', '-ms-flexbox');
            $(this).find('span').text('See Less');
        });

    });
</script>
<?php
if ( ! wp_doing_ajax() ) {
	do_action( 'woocommerce_review_order_after_payment' );
}
